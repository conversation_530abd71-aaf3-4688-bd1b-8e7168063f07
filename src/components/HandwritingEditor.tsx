import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { TypographyConfigPanel, TypographyConfig } from '@/components/ui/typography-config';
import { LaTeXRenderer, hasLaTeX } from '@/components/ui/latex-renderer';
import { Download, Type, FileText, Palette, Image, Settings } from 'lucide-react';
import html2pdf from 'html2pdf.js';
import html2canvas from 'html2canvas';
import { motion } from 'motion/react';

const HandwritingEditor = () => {
  const [text, setText] = useState('');
  const [selectedFont, setSelectedFont] = useState('handwriting');

  const [background, setBackground] = useState('lined');
  const [typographyConfig, setTypographyConfig] = useState<TypographyConfig>({
    fontSize: 18,
    lineHeight: 1.6,
    letterSpacing: 0,
    wordSpacing: 0,
    textColor: '#000000'
  });
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const paperRef = useRef<HTMLDivElement>(null);

  const fontOptions = [
    { value: 'handwriting', label: 'Kalam (Default)', class: 'font-handwriting' },
    { value: 'handwriting-alt', label: 'Caveat', class: 'font-handwriting-alt' },
    { value: 'handwriting-dance', label: 'Dancing Script', class: 'font-handwriting-dance' },
    { value: 'handwriting-architect', label: 'Architects Daughter', class: 'font-handwriting-architect' },
    { value: 'handwriting-indie', label: 'Indie Flower', class: 'font-handwriting-indie' },
  ];



  const backgroundOptions = [
    { value: 'lined', label: 'Lined Paper' },
    { value: 'plain', label: 'Plain White' },
    { value: 'dots', label: 'Dotted Paper' },
    { value: 'graph', label: 'Graph Paper' },
    { value: 'music', label: 'Music Staff' },
    { value: 'parchment', label: 'Vintage Parchment' },
    { value: 'blueprint', label: 'Blueprint Style' },
  ];

  const selectedFontClass = fontOptions.find(f => f.value === selectedFont)?.class || 'font-handwriting';

  const generatePDF = async () => {
    if (!paperRef.current || !text.trim()) {
      alert('Please write some text before generating PDF');
      return;
    }

    const element = paperRef.current;
    const opt = {
      margin: 0,
      filename: 'handwritten-note.pdf',
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff'
      },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    };

    try {
      await html2pdf().set(opt).from(element).save();
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  const generateImage = async () => {
    if (!paperRef.current || !text.trim()) {
      alert('Please write some text before generating image');
      return;
    }

    try {
      const canvas = await html2canvas(paperRef.current, {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff',
        logging: false
      });

      // Create download link
      const link = document.createElement('a');
      link.download = 'handwritten-note.png';
      link.href = canvas.toDataURL('image/png');
      link.click();
    } catch (error) {
      console.error('Error generating image:', error);
      alert('Error generating image. Please try again.');
    }
  };

  const renderPaperBackground = () => {
    const textStyle = {
      fontSize: `${typographyConfig.fontSize}px`,
      lineHeight: background === 'lined' ? '25px' : typographyConfig.lineHeight,
      letterSpacing: `${typographyConfig.letterSpacing}px`,
      wordSpacing: `${typographyConfig.wordSpacing}px`,
      color: background === 'blueprint' ? '#ffffff' : typographyConfig.textColor
    };

    const textContent = hasLaTeX(text) ? (
      <LaTeXRenderer
        text={text || 'Your handwritten text will appear here...'}
        className={`${selectedFontClass} whitespace-pre-wrap`}
        style={textStyle}
      />
    ) : (
      <div
        className={`${selectedFontClass} whitespace-pre-wrap`}
        style={textStyle}
      >
        {text || 'Your handwritten text will appear here...'}
      </div>
    );

    switch (background) {
      case 'plain':
        return (
          <div className="w-full h-full p-8 relative bg-white">
            {textContent}
          </div>
        );

      case 'dots':
        return (
          <div 
            className="w-full h-full p-8 relative bg-white"
            style={{
              backgroundImage: `radial-gradient(circle, #d1d5db 1px, transparent 1px)`,
              backgroundSize: '20px 20px',
              backgroundPosition: '10px 10px'
            }}
          >
            {textContent}
          </div>
        );

      case 'graph':
        return (
          <div 
            className="w-full h-full p-8 relative bg-white"
            style={{
              backgroundImage: `
                linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          >
            {textContent}
          </div>
        );

      case 'music':
        return (
          <div 
            className="w-full h-full p-8 relative bg-white"
            style={{
              backgroundImage: `
                repeating-linear-gradient(
                  0deg,
                  transparent,
                  transparent 30px,
                  #000000 30px,
                  #000000 31px,
                  transparent 31px,
                  transparent 40px,
                  #000000 40px,
                  #000000 41px,
                  transparent 41px,
                  transparent 50px,
                  #000000 50px,
                  #000000 51px,
                  transparent 51px,
                  transparent 60px,
                  #000000 60px,
                  #000000 61px,
                  transparent 61px,
                  transparent 70px,
                  #000000 70px,
                  #000000 71px,
                  transparent 71px,
                  transparent 120px
                )
              `
            }}
          >
            <div className="pt-8">
              {textContent}
            </div>
          </div>
        );

      case 'parchment':
        return (
          <div 
            className="w-full h-full p-8 relative"
            style={{
              background: `linear-gradient(45deg, #f4f1e8 25%, #f7f4ec 25%, #f7f4ec 50%, #f4f1e8 50%, #f4f1e8 75%, #f7f4ec 75%, #f7f4ec)`,
              backgroundColor: '#f9f6f0',
              backgroundSize: '20px 20px',
              boxShadow: 'inset 0 0 50px rgba(139, 69, 19, 0.1)'
            }}
          >
            <div style={{ color: '#8b4513' }}>
              {textContent}
            </div>
          </div>
        );

      case 'blueprint':
        return (
          <div 
            className="w-full h-full p-8 relative"
            style={{
              backgroundColor: '#1e3a8a',
              backgroundImage: `
                linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          >
            {textContent}
          </div>
        );

      default: // lined
        return (
          <div 
            className="w-full h-full p-8 relative bg-white"
            style={{
              backgroundImage: `
                linear-gradient(to bottom, 
                  transparent 0px, 
                  transparent 24px, 
                  #e5e7eb 24px, 
                  #e5e7eb 25px, 
                  transparent 25px
                )`,
              backgroundSize: '100% 25px',
              backgroundRepeat: 'repeat-y'
            }}
          >
            {/* Red margin line */}
            <div className="absolute left-12 top-0 bottom-0 w-px bg-red-300"></div>
            
            {/* Text content */}
            <div className="pl-8">
              {textContent}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-black relative overflow-hidden">
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px] dark:bg-[linear-gradient(to_right,#ffffff0f_1px,transparent_1px),linear-gradient(to_bottom,#ffffff0f_1px,transparent_1px)]"></div>
      
      {/* Radial Gradient Overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"></div>
      
      <div className="relative z-10 p-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8"
          >
            <h1 className="text-4xl font-bold text-neutral-800 dark:text-neutral-200 mb-2 flex items-center justify-center gap-3">
              <FileText className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
              Handwritten Text to PDF
            </h1>
            <p className="text-neutral-600 dark:text-neutral-400">Transform your text into beautiful handwritten-style PDFs</p>
          </motion.div>

          {/* Controls */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="p-6 mb-6 bg-white/80 dark:bg-black/40 backdrop-blur-md border border-neutral-200/20 dark:border-neutral-800/20 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:shadow-[0_0_24px_rgba(0,_0,_0,_0.3)]">
              <div className="flex flex-wrap gap-4 items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Type className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
                    <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">Font:</span>
                  </div>
                  <Select value={selectedFont} onValueChange={setSelectedFont}>
                    <SelectTrigger className="w-48 bg-white/50 dark:bg-black/50 border-neutral-200/20 dark:border-neutral-700/20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {fontOptions.map((font) => (
                        <SelectItem key={font.value} value={font.value}>
                          <span className={font.class}>{font.label}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Palette className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
                    <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">Background:</span>
                  </div>
                  <Select value={background} onValueChange={setBackground}>
                    <SelectTrigger className="w-40 bg-white/50 dark:bg-black/50 border-neutral-200/20 dark:border-neutral-700/20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {backgroundOptions.map((bg) => (
                        <SelectItem key={bg.value} value={bg.value}>
                          {bg.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  variant="outline"
                  onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                  className="bg-white/50 dark:bg-black/50 border-neutral-200/20 dark:border-neutral-700/20"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Typography
                </Button>

                <div className="flex gap-2">
                  <Button
                    onClick={generateImage}
                    disabled={!text.trim()}
                    variant="outline"
                    className="bg-white/50 dark:bg-black/50 border-neutral-200/20 dark:border-neutral-700/20"
                  >
                    <Image className="h-4 w-4 mr-2" />
                    Image
                  </Button>

                  <button
                    onClick={generatePDF}
                    disabled={!text.trim()}
                    className="px-8 py-0.5 border-2 border-black dark:border-white uppercase bg-white text-black transition duration-200 text-sm shadow-[1px_1px_rgba(0,0,0),2px_2px_rgba(0,0,0),3px_3px_rgba(0,0,0),4px_4px_rgba(0,0,0),5px_5px_0px_0px_rgba(0,0,0)] dark:shadow-[1px_1px_rgba(255,255,255),2px_2px_rgba(255,255,255),3px_3px_rgba(255,255,255),4px_4px_rgba(255,255,255),5px_5px_0px_0px_rgba(255,255,255)]"
                  >
                    Download PDF
                    <Download className="h-4 w-4 inline-block ml-1" />
                  </button>
                </div>
              </div>

              {/* Advanced Typography Settings */}
              {showAdvancedSettings && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <TypographyConfigPanel
                    config={typographyConfig}
                    onChange={setTypographyConfig}
                    className="bg-white/30 dark:bg-black/30"
                  />
                </motion.div>
              )}
            </Card>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Input Area */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="p-6 bg-white/80 dark:bg-black/40 backdrop-blur-md border border-neutral-200/20 dark:border-neutral-800/20 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:shadow-[0_0_24px_rgba(0,_0,_0,_0.3)] hover:shadow-lg transition-shadow duration-300">
                <h2 className="text-xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200">Write Your Text</h2>
                <Textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  placeholder="Start writing your text here... It will appear in handwritten style on the right!

LaTeX Support:
• Inline math: $x^2 + y^2 = z^2$
• Display math: $$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
• Fractions: $\frac{a}{b}$, Greek letters: $\alpha, \beta, \gamma$"
                  className="min-h-[500px] resize-none text-base leading-relaxed bg-white/50 dark:bg-black/20 border-neutral-200/20 dark:border-neutral-700/20 focus:border-indigo-500/50 dark:focus:border-indigo-400/50"
                />
                <div className="mt-4 text-sm text-neutral-500 dark:text-neutral-400 space-y-1">
                  <div>Characters: {text.length}</div>
                  {hasLaTeX(text) && (
                    <div className="text-green-600 dark:text-green-400">
                      ✓ LaTeX detected and will be rendered
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>

            {/* Preview Area */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card className="p-6 bg-white/80 dark:bg-black/40 backdrop-blur-md border border-neutral-200/20 dark:border-neutral-800/20 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:shadow-[0_0_24px_rgba(0,_0,_0,_0.3)] hover:shadow-lg transition-shadow duration-300">
                <h2 className="text-xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200">Preview (A4 Format)</h2>
                <div className="relative overflow-hidden">
                  <div 
                    ref={paperRef}
                    className="w-full shadow-lg rounded-lg overflow-hidden"
                    style={{
                      aspectRatio: '210/297', // A4 ratio
                      maxHeight: '500px'
                    }}
                  >
                    {renderPaperBackground()}
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-8 text-center text-neutral-500 dark:text-neutral-400 text-sm space-y-2"
          >
            <p>Your text will be converted to PDF or PNG with handwritten-style fonts</p>
            <p>✨ Features: LaTeX math support, custom typography, ink colors, and multiple paper styles</p>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default HandwritingEditor;
